
import React from 'react';
import { Card, CardContent, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, ChevronRight, Star, Sparkles, Crown, Zap } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion } from "framer-motion";
import { cn } from '@/lib/utils';
import { toast } from "sonner";

interface PricingPlanProps {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  features: string[];
  perks?: string[];
  index: number;
  recommended?: boolean;
  mostPopular?: boolean;
  badge?: string | null;
  discountPercentage?: string | null;
  validityPeriod?: string;
  onChoosePlan: () => void;
  variant?: 'plus' | 'pro' | 'max' | 'basic';
}

const PricingCard: React.FC<PricingPlanProps> = ({
  name,
  price,
  originalPrice,
  features,
  perks = [],
  index,
  recommended = false,
  mostPopular = false,
  badge = null,
  discountPercentage = null,
  validityPeriod,
  onChoosePlan,
  variant = 'plus'
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'max':
        return {
          gradient: 'from-purple-600 via-purple-500 to-indigo-600',
          textColor: 'text-purple-400',
          borderColor: 'border-purple-500/30',
          glowColor: 'shadow-purple-500/25',
          bgGradient: 'from-purple-900/20 via-purple-800/10 to-indigo-900/20',
          icon: Crown
        };
      case 'pro':
        return {
          gradient: 'from-blue-600 via-blue-500 to-cyan-600',
          textColor: 'text-blue-400',
          borderColor: 'border-blue-500/30',
          glowColor: 'shadow-blue-500/25',
          bgGradient: 'from-blue-900/20 via-blue-800/10 to-cyan-900/20',
          icon: Zap
        };
      case 'basic':
        return {
          gradient: 'from-pegasus-blue-600 via-pegasus-blue-500 to-pegasus-blue-400',
          textColor: 'text-pegasus-blue-400',
          borderColor: 'border-pegasus-blue-500/30',
          glowColor: 'shadow-pegasus-blue-500/25',
          bgGradient: 'from-pegasus-blue-900/20 via-pegasus-blue-800/10 to-pegasus-blue-700/20',
          icon: Star
        };
      default:
        return {
          gradient: 'from-orange-600 via-orange-500 to-amber-600',
          textColor: 'text-orange-400',
          borderColor: 'border-orange-500/30',
          glowColor: 'shadow-orange-500/25',
          bgGradient: 'from-orange-900/20 via-orange-800/10 to-amber-900/20',
          icon: Sparkles
        };
    }
  };

  const handleContactSales = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
    toast.info(`For more information about our ${name} plan, please contact our sales team.`);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        delay: index * 0.2,
        ease: [0.22, 1, 0.36, 1]
      }
    },
    hover: {
      y: -12,
      scale: 1.02,
      transition: { duration: 0.3, ease: "easeOut" }
    },
    exit: {
      opacity: 0,
      y: -50,
      scale: 0.9,
      transition: {
        duration: 0.5
      }
    }
  };

  const variantStyles = getVariantStyles();
  const IconComponent = variantStyles.icon;

  return (
    <motion.div
      className="h-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      whileHover="hover"
    >
      <Card
        className={cn(
          "h-full flex flex-col overflow-hidden relative group",
          "bg-gradient-to-br from-[#1a1a1a] via-[#1e1e1e] to-[#1a1a1a]",
          "border border-gray-700/50 backdrop-blur-xl rounded-2xl",
          "transition-all duration-500 ease-out",
          "hover:transform hover:scale-[1.02] hover:-translate-y-2",
          mostPopular && [
            "ring-2 ring-orange-400/40 border-orange-400/60",
            "shadow-2xl shadow-orange-500/25",
            "transform scale-105",
            "before:absolute before:inset-0 before:bg-gradient-to-br before:from-orange-500/5 before:to-red-500/5 before:rounded-2xl before:opacity-0 before:group-hover:opacity-100 before:transition-opacity before:duration-500"
          ],
          recommended && [
            "ring-2 ring-purple-400/40 border-purple-400/60",
            "shadow-2xl shadow-purple-500/25",
            "transform scale-105",
            "before:absolute before:inset-0 before:bg-gradient-to-br before:from-purple-500/5 before:to-indigo-500/5 before:rounded-2xl before:opacity-0 before:group-hover:opacity-100 before:transition-opacity before:duration-500"
          ],
          !mostPopular && !recommended && [
            "hover:border-gray-600/70 hover:shadow-xl hover:shadow-gray-900/40",
            "before:absolute before:inset-0 before:bg-gradient-to-br before:from-gray-500/3 before:to-gray-600/3 before:rounded-2xl before:opacity-0 before:group-hover:opacity-100 before:transition-opacity before:duration-500"
          ]
        )}
      >
        {/* Background Gradient Overlay */}
        <div className={cn(
          "absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500",
          `bg-gradient-to-br ${variantStyles.bgGradient}`,
          "rounded-2xl"
        )} />

        {/* Animated Background Particles */}
        <div className="absolute inset-0 overflow-hidden rounded-2xl">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className={cn(
                "absolute w-2 h-2 rounded-full opacity-20",
                `bg-gradient-to-r ${variantStyles.gradient}`
              )}
              animate={{
                x: [0, 100, 0],
                y: [0, -50, 0],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 4 + i,
                repeat: Infinity,
                delay: i * 1.5,
                ease: "easeInOut"
              }}
              style={{
                left: `${20 + i * 30}%`,
                top: `${30 + i * 20}%`,
              }}
            />
          ))}
        </div>

        {/* Top Accent Line */}
        <div className={cn(
          "h-1 w-full bg-gradient-to-r",
          variantStyles.gradient,
          "relative overflow-hidden"
        )}>
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            animate={{
              x: ["-100%", "100%"],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 3,
              ease: "easeInOut"
            }}
          />
        </div>

        {/* Badge for Most Popular, Recommended, etc. */}
        {badge && (
          <motion.div
            initial={{ scale: 0, rotate: -15, y: -10 }}
            animate={{ scale: 1, rotate: 0, y: 0 }}
            transition={{ delay: 0.4, type: "spring", stiffness: 200, damping: 10 }}
            className={cn(
              "absolute -top-5 -right-5 z-30",
              "px-5 py-2.5 rounded-2xl text-xs font-bold uppercase tracking-wider",
              "border-2 border-[#111111] shadow-2xl",
              "flex items-center gap-2",
              "backdrop-blur-sm",
              "hover:scale-110 transition-transform duration-300",
              mostPopular && [
                "bg-gradient-to-r from-orange-500 via-red-500 to-orange-600 text-white",
                "shadow-orange-500/30 hover:shadow-orange-500/50"
              ],
              recommended && [
                "bg-gradient-to-r from-purple-500 via-indigo-500 to-purple-600 text-white",
                "shadow-purple-500/30 hover:shadow-purple-500/50"
              ],
              !mostPopular && !recommended && [
                "bg-gradient-to-r from-blue-500 via-cyan-500 to-blue-600 text-white",
                "shadow-blue-500/30 hover:shadow-blue-500/50"
              ]
            )}
            whileHover={{
              rotate: [0, -3, 3, 0],
              transition: { duration: 0.5 }
            }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            >
              <IconComponent className="h-4 w-4" />
            </motion.div>
            {badge}
            {/* Badge shine effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-2xl"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        )}

        {/* Discount Badge */}
        {discountPercentage && (
          <motion.div
            initial={{ scale: 0, y: -10 }}
            animate={{ scale: 1, y: 0 }}
            transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
            className={cn(
              "absolute -top-3 -left-3 z-20",
              "px-3 py-1.5 rounded-full text-xs font-bold",
              "bg-gradient-to-r from-red-500 to-red-600 text-white",
              "border-2 border-[#111111] shadow-lg",
              "animate-pulse"
            )}
          >
            {discountPercentage} OFF
          </motion.div>
        )}
        <CardHeader className="pt-10 pb-6 relative z-10">
          {/* Plan Icon */}
          <motion.div
            className={cn(
              "w-20 h-20 mx-auto mb-6 rounded-3xl relative",
              "flex items-center justify-center",
              "bg-gradient-to-br", variantStyles.bgGradient,
              "border", variantStyles.borderColor,
              "shadow-xl", variantStyles.glowColor,
              "group-hover:shadow-2xl transition-all duration-500"
            )}
            whileHover={{
              rotate: [0, -8, 8, 0],
              scale: 1.1,
              transition: { duration: 0.6, ease: "easeInOut" }
            }}
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.8, type: "spring", stiffness: 200 }}
          >
            {/* Icon glow effect */}
            <div className={cn(
              "absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500",
              "bg-gradient-to-br", variantStyles.gradient,
              "blur-xl scale-110"
            )} />
            <IconComponent className={cn("h-10 w-10 relative z-10", variantStyles.textColor)} />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <CardTitle className={cn(
              "text-3xl font-bold text-center mb-6 relative",
              variantStyles.textColor,
              "group-hover:scale-105 transition-transform duration-300"
            )}>
              {name}
              {/* Text glow effect */}
              <div className={cn(
                "absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-500",
                "bg-gradient-to-r", variantStyles.gradient,
                "bg-clip-text text-transparent blur-sm scale-110"
              )}>
                {name}
              </div>
            </CardTitle>
          </motion.div>

          {/* Price Section */}
          <div className="text-center">
            <div className="flex items-baseline justify-center mb-2">
              {originalPrice && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="text-lg text-gray-500 font-normal line-through mr-3"
                >
                  ${originalPrice}
                </motion.span>
              )}
              <motion.span
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
                className={cn(
                  "text-6xl font-bold relative",
                  "bg-gradient-to-br from-white via-gray-100 to-gray-300",
                  "bg-clip-text text-transparent",
                  "group-hover:scale-110 transition-transform duration-300"
                )}
              >
                ${price}
                {/* Price glow effect */}
                <div className={cn(
                  "absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500",
                  "bg-gradient-to-r", variantStyles.gradient,
                  "bg-clip-text text-transparent blur-sm"
                )}>
                  ${price}
                </div>
              </motion.span>
            </div>

            {/* Validity Period */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-center"
            >
              <span className="text-sm text-gray-400 font-medium px-3 py-1 bg-gray-800/50 rounded-full border border-gray-700/50">
                {validityPeriod || 'Per Month'}
              </span>
            </motion.div>
          </div>
        </CardHeader>
        <CardContent className="px-8 pb-8 flex-grow relative z-10">
          {/* Features List */}
          <motion.ul
            className="space-y-4 mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, staggerChildren: 0.1 }}
          >
            {features.map((feature, i) => (
              <motion.li
                key={i}
                className="flex items-start group"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + (i * 0.1) }}
              >
                <motion.div
                  className={cn(
                    "h-6 w-6 mr-3 shrink-0 mt-0.5 rounded-full",
                    "flex items-center justify-center",
                    "bg-gradient-to-br", variantStyles.bgGradient,
                    "border", variantStyles.borderColor,
                    "group-hover:scale-110 transition-transform duration-200"
                  )}
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.3 }}
                >
                  <Check className={cn("h-3.5 w-3.5", variantStyles.textColor)} strokeWidth={3} />
                </motion.div>
                <span className="text-gray-300 leading-relaxed font-medium">{feature}</span>
              </motion.li>
            ))}
          </motion.ul>

          {/* Extra Perks */}
          {perks.length > 0 && (
            <motion.div
              className="mt-8 pt-6 border-t border-gray-700/50"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <h4 className={cn(
                "font-semibold mb-4 flex items-center gap-2",
                variantStyles.textColor
              )}>
                <Sparkles className="h-4 w-4" />
                Extra Perks
              </h4>
              <ul className="space-y-3">
                {perks.map((perk, i) => (
                  <motion.li
                    key={i}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7 + (i * 0.1) }}
                  >
                    <div className="h-5 w-5 mr-3 shrink-0 mt-0.5 text-amber-400 flex items-center justify-center">
                      <Check className="h-3 w-3" strokeWidth={3} />
                    </div>
                    <span className="text-gray-400 text-sm leading-relaxed">{perk}</span>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          )}
        </CardContent>
        <CardFooter className="px-8 py-6 mt-auto relative z-10">
          <motion.div
            className="w-full"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className={cn(
                    "w-full h-12 font-semibold text-white shadow-lg",
                    "bg-gradient-to-r", variantStyles.gradient,
                    "hover:shadow-xl hover:scale-105",
                    "transition-all duration-300 ease-out",
                    "border border-white/10",
                    "relative overflow-hidden group"
                  )}
                >
                  {/* Button shine effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    animate={{
                      x: ["-100%", "100%"],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 4,
                      ease: "easeInOut"
                    }}
                  />
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    Choose Plan
                    <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-48 bg-[#1a1a1a] border border-gray-700/50 backdrop-blur-xl"
              >
                <DropdownMenuItem
                  onClick={onChoosePlan}
                  className="cursor-pointer text-gray-300 hover:text-white hover:bg-gray-800/50 focus:bg-gray-800/50"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Purchase Now
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleContactSales}
                  className="cursor-pointer text-gray-300 hover:text-white hover:bg-gray-800/50 focus:bg-gray-800/50"
                >
                  <Star className="h-4 w-4 mr-2" />
                  Contact Sales
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => window.location.href = '/faq'}
                  className="cursor-pointer text-gray-300 hover:text-white hover:bg-gray-800/50 focus:bg-gray-800/50"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Read FAQ
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </motion.div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default PricingCard;
