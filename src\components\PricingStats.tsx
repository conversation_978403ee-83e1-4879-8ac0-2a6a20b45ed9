import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Users, Clock, Shield, Zap, Award } from 'lucide-react';
import { cn } from '@/lib/utils';
import StatisticsStyleHeader from '@/components/layout/StatisticsStyleHeader';

interface PricingStatsProps {
  theme?: 'software' | 'hardware';
}

const PricingStats: React.FC<PricingStatsProps> = ({ theme = 'software' }) => {
  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400',
        accent: 'pegasus-blue'
      };
    }
    return {
      primary: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400',
      accent: 'orange'
    };
  };

  const themeColors = getThemeColors();

  const stats = [
    {
      icon: Users,
      value: '10,000+',
      label: 'Active Users',
      description: 'Technicians trust our tools',
      color: 'text-blue-600 dark:text-blue-400'
    },
    {
      icon: TrendingUp,
      value: '500K+',
      label: 'Successful Repairs',
      description: 'Operations completed',
      color: 'text-green-600 dark:text-green-400'
    },
    {
      icon: Clock,
      value: '99.9%',
      label: 'Uptime',
      description: 'Reliable service',
      color: themeColors.primary
    },
    {
      icon: Shield,
      value: '24/7',
      label: 'Support',
      description: 'Always here to help',
      color: 'text-purple-600 dark:text-purple-400'
    },
    {
      icon: Zap,
      value: '< 2s',
      label: 'Response Time',
      description: 'Lightning fast',
      color: 'text-yellow-600 dark:text-yellow-400'
    },
    {
      icon: Award,
      value: '4.9/5',
      label: 'Customer Rating',
      description: 'Highly rated service',
      color: 'text-red-600 dark:text-red-400'
    }
  ];

  const benefits = [
    {
      title: 'No Setup Fees',
      description: 'Start using immediately without any hidden costs',
      icon: '🚀'
    },
    {
      title: 'Cancel Anytime',
      description: 'No long-term contracts or commitments',
      icon: '✨'
    },
    {
      title: 'Data Security',
      description: 'Enterprise-grade security for your data',
      icon: '🔒'
    },
    {
      title: 'Regular Updates',
      description: 'New features and improvements monthly',
      icon: '🔄'
    }
  ];

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <StatisticsStyleHeader
          badge="Platform Statistics"
          title="Trusted by Professionals Worldwide"
          highlightWord="Professionals"
          subtitle="Join thousands of technicians who rely on our platform for their daily operations"
        />

        <motion.div
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mt-12"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-50px" }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              variants={item}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
              className="text-center"
            >
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/20">
                <motion.div
                  className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-4 mx-auto shadow-md"
                  whileHover={{ rotate: [0, -10, 10, -10, 0], transition: { duration: 0.5 } }}
                >
                  <stat.icon className={cn("h-6 w-6", stat.color)} />
                </motion.div>
                <div className="text-2xl font-bold text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm font-semibold text-gray-300 mb-1">
                  {stat.label}
                </div>
                <div className="text-xs text-gray-400">
                  {stat.description}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        </div>

        {/* Benefits Section */}
        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h4 className="text-2xl font-bold text-white mb-4">
              Why Choose Our <span className={themeColors.primary}>Platform?</span>
            </h4>
            <p className="text-gray-400 max-w-2xl mx-auto">
              We've designed our pricing to be transparent, flexible, and value-driven
            </p>
          </div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
          >
            {benefits.map((benefit) => (
              <motion.div
                key={benefit.title}
                variants={item}
                whileHover={{
                  y: -8,
                  scale: 1.02,
                  transition: { duration: 0.3 }
                }}
                className="text-center"
              >
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/20 h-full flex flex-col">
                  <div className="text-4xl mb-4">{benefit.icon}</div>
                  <h5 className="text-lg font-semibold text-white mb-2">
                    {benefit.title}
                  </h5>
                  <p className="text-gray-400 text-sm flex-grow">
                    {benefit.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Value Proposition */}
        <motion.div
          className="mt-16 text-center max-w-5xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-3xl p-8 md:p-12 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/20">
            <h4 className="text-3xl font-bold mb-4 text-white">
              Start Your Journey <span className={themeColors.primary}>Today</span>
            </h4>
            <p className="text-gray-300 text-lg mb-8 max-w-3xl mx-auto">
              Choose the plan that fits your needs and join thousands of satisfied technicians.
              All plans include our core features with no hidden fees.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <motion.div
                className="flex items-center gap-2 text-green-400"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <Shield className="h-5 w-5" />
                <span className="text-sm font-semibold">30-Day Money Back Guarantee</span>
              </motion.div>
              <motion.div
                className="flex items-center gap-2 text-blue-400"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <Zap className="h-5 w-5" />
                <span className="text-sm font-semibold">Instant Activation</span>
              </motion.div>
              <motion.div
                className="flex items-center gap-2 text-purple-400"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <Award className="h-5 w-5" />
                <span className="text-sm font-semibold">Premium Support</span>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Pricing Transparency */}
        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-8">
            <h4 className="text-2xl font-bold text-white mb-4">
              💡 Pricing Transparency
            </h4>
          </div>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
          >
            <motion.div
              variants={item}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-green-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-green-500/20 h-full">
                <h5 className="font-semibold text-white mb-4 text-center">
                  ✅ What's Included
                </h5>
                <ul className="text-sm text-gray-400 space-y-2">
                  <li>• All listed features</li>
                  <li>• Regular updates</li>
                  <li>• Email support</li>
                  <li>• Data backup</li>
                </ul>
              </div>
            </motion.div>
            <motion.div
              variants={item}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-red-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-red-500/20 h-full">
                <h5 className="font-semibold text-white mb-4 text-center">
                  ❌ No Hidden Fees
                </h5>
                <ul className="text-sm text-gray-400 space-y-2">
                  <li>• No setup costs</li>
                  <li>• No cancellation fees</li>
                  <li>• No per-user charges</li>
                  <li>• No data export fees</li>
                </ul>
              </div>
            </motion.div>
            <motion.div
              variants={item}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-blue-500/20 h-full">
                <h5 className="font-semibold text-white mb-4 text-center">
                  🔄 Flexible Terms
                </h5>
                <ul className="text-sm text-gray-400 space-y-2">
                  <li>• Monthly or yearly billing</li>
                  <li>• Upgrade/downgrade anytime</li>
                  <li>• Pause subscription option</li>
                  <li>• Pro-rated refunds</li>
                </ul>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
  );
};

export default PricingStats;
