
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, <PERSON>R<PERSON>, ChevronLeft, ChevronRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import PricingCard from '@/components/PricingCard';
import PlanComparison from '@/components/PlanComparison';
import PricingTestimonials from '@/components/PricingTestimonials';
import PricingFAQ from '@/components/PricingFAQ';
import AdvancedOfferBanner from '@/components/AdvancedOfferBanner';
import PricingStats from '@/components/PricingStats';

interface PricingPlan {
  id: string;
  name_plan: string;
  price: string;
  features: string;
  perks: string | null;
  validity_period?: string;
  unlock_limit?: number;
  readcode_limit?: number;
  other_operations_limit?: string;
  ai_access?: string;
}



interface AdvancedOffer {
  id: string;
  title: string;
  description: string;
  discount_type: 'percentage' | 'fixed' | 'bogo' | 'tiered';
  discount_value: number;
  applicable_plans: string[];
  end_date: string;
  urgency_level: 'low' | 'medium' | 'high';
  badge_text: string;
  promo_code?: string;
  is_active: boolean;
}

interface PricingProps {
  theme?: 'software' | 'hardware';
  showAllComponents?: boolean;
}

const Pricing: React.FC<PricingProps> = ({ theme = 'software', showAllComponents = true }) => {
  const { toast: toastNotify } = useToast();
  const navigate = useNavigate();
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [advancedOffer, setAdvancedOffer] = useState<AdvancedOffer | null>(null);
  const [appliedPromoCode, setAppliedPromoCode] = useState<string | null>(null);
  const [promoOffer, setPromoOffer] = useState<AdvancedOffer | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);

  // Pagination constants
  const CARDS_PER_PAGE = 3;

  useEffect(() => {
    const fetchPricingPlans = async () => {
      try {
        // Fetch pricing plans
        const { data: plansData, error: plansError } = await supabase
          .from('pricing')
          .select('*')
          .order('price');

        if (plansError) throw plansError;

        // Fetch advanced offers
        const now = new Date().toISOString();
        const { data: advancedOffersData, error: advancedOffersError } = await supabase
          .from('advanced_offers')
          .select('*')
          .eq('is_active', true)
          .gt('end_date', now)
          .order('urgency_level', { ascending: false })
          .order('created_at', { ascending: false })
          .limit(1);

        if (advancedOffersError) {
          console.warn('Advanced offers not available:', advancedOffersError);
        } else if (advancedOffersData && advancedOffersData.length > 0) {
          setAdvancedOffer(advancedOffersData[0]);
        }

        // Sort and organize plans for better display
        const sortedPlans = sortPlansForDisplay(plansData || []);
        setPlans(sortedPlans);
      } catch (error) {
        toastNotify({
          title: "Error",
          description: "Failed to fetch pricing information. Please try again later.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPricingPlans();
  }, [toastNotify]);

  // Function to sort plans for better display
  const sortPlansForDisplay = (plans: PricingPlan[]): PricingPlan[] => {
    // Define the preferred order for plans
    const planOrder = ['basic', 'plus', 'pro', 'max', 'max pro', 'max pro plus'];

    return plans.sort((a, b) => {
      const aName = a.name_plan.toLowerCase();
      const bName = b.name_plan.toLowerCase();

      // Find the index in our preferred order
      const aIndex = planOrder.findIndex(order => aName.includes(order));
      const bIndex = planOrder.findIndex(order => bName.includes(order));

      // If both plans are in our order list, sort by that order
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }

      // If only one is in the order list, prioritize it
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;

      // If neither is in the order list, sort by price
      const aPrice = parseFloat(a.price) || 0;
      const bPrice = parseFloat(b.price) || 0;
      return aPrice - bPrice;
    });
  };

  // Function to generate features from plan data
  const generateFeatures = (plan: PricingPlan): string[] => {
    const features: string[] = [];

    // Add unlock limit feature
    if (plan.unlock_limit) {
      features.push(`${plan.unlock_limit} Unlock Operations`);
    }

    // Add read code feature
    if (plan.readcode_limit && plan.readcode_limit > 0) {
      features.push(`${plan.readcode_limit} Read Code Operations`);
    }

    // Add other operations feature
    if (plan.other_operations_limit) {
      const limit = plan.other_operations_limit === 'Unlimited' ? 'Unlimited' : plan.other_operations_limit;
      features.push(`${limit} Other Operations`);
    }

    // Add AI access feature
    if (plan.ai_access) {
      features.push(`${plan.ai_access} AI Assistant Access`);
    }

    // Add validity period
    if (plan.validity_period) {
      features.push(`Valid ${plan.validity_period}`);
    }

    // Parse additional features if available
    if (plan.features) {
      try {
        const additionalFeatures = plan.features.split('\n').map(feature => feature.trim()).filter(Boolean);
        features.push(...additionalFeatures);
      } catch (e) {
        // Ignore parsing errors
      }
    }

    return features;
  };

  // Function to parse perks string into an array
  const parsePerks = (perksStr: string | null): string[] => {
    if (!perksStr) return [];
    try {
      return perksStr.split('\n').map(perk => perk.trim()).filter(Boolean);
    } catch (e) {
      return [];
    }
  };

  // Enhanced function to calculate discounted price with advanced offers support
  const calculateDiscountedPrice = (originalPrice: string, planName: string): {
    original: string;
    discounted: string | null;
    savings: string | null;
    discountPercentage: string | null;
    offerType: string | null;
  } => {
    const price = parseFloat(originalPrice);
    let bestDiscount = { amount: 0, percentage: 0, type: '', savings: 0 };

    // Helper function to calculate discount from an offer
    const calculateOfferDiscount = (offer: AdvancedOffer) => {
      if (!offer.applicable_plans.includes(planName)) return null;

      let discountAmount = 0;
      let discountPercentage = 0;

      switch (offer.discount_type) {
        case 'percentage':
          discountPercentage = offer.discount_value;
          discountAmount = price * (discountPercentage / 100);
          break;
        case 'fixed':
          discountAmount = Math.min(offer.discount_value, price);
          discountPercentage = (discountAmount / price) * 100;
          break;
        case 'tiered':
          // Simple tiered logic - could be enhanced
          if (price >= 100) discountPercentage = offer.discount_value;
          else if (price >= 50) discountPercentage = offer.discount_value * 0.75;
          else discountPercentage = offer.discount_value * 0.5;
          discountAmount = price * (discountPercentage / 100);
          break;
      }

      return {
        amount: discountAmount,
        percentage: discountPercentage,
        type: offer.title,
        savings: discountAmount
      };
    };

    // Check general advanced offer
    if (advancedOffer) {
      const offerDiscount = calculateOfferDiscount(advancedOffer);
      if (offerDiscount && offerDiscount.amount > bestDiscount.amount) {
        bestDiscount = offerDiscount;
      }
    }

    // Check promo code offer (if different from general offer)
    if (promoOffer && promoOffer.id !== advancedOffer?.id) {
      const promoDiscount = calculateOfferDiscount(promoOffer);
      if (promoDiscount && promoDiscount.amount > bestDiscount.amount) {
        bestDiscount = promoDiscount;
      }
    }

    if (bestDiscount.amount === 0) {
      return { original: originalPrice, discounted: null, savings: null, discountPercentage: null, offerType: null };
    }

    const discountedPrice = price - bestDiscount.amount;

    return {
      original: originalPrice,
      discounted: discountedPrice.toFixed(2),
      savings: bestDiscount.savings.toFixed(2),
      discountPercentage: `${Math.round(bestDiscount.percentage)}%`,
      offerType: bestDiscount.type
    };
  };

  const handleChoosePlan = (plan: PricingPlan) => {
    toast(`You've selected the ${plan.name_plan} plan`, {
      description: "Contact sales for next steps.",
      action: {
        label: "Contact Sales",
        onClick: () => {
          const contactSection = document.getElementById('contact');
          if (contactSection) {
            contactSection.scrollIntoView({ behavior: 'smooth' });
          }
        }
      }
    });
  };

  const handleApplyPromoCode = async (code: string) => {
    try {
      // Verify promo code exists and is valid
      const { data: promoData, error: promoError } = await supabase
        .from('advanced_offers')
        .select('*')
        .eq('promo_code', code.toUpperCase())
        .eq('is_active', true)
        .gt('end_date', new Date().toISOString())
        .single();

      if (promoError || !promoData) {
        toast('Invalid or expired promo code', {
          description: 'Please check your code and try again.',
        });
        return;
      }

      setAppliedPromoCode(code.toUpperCase());
      setPromoOffer(promoData);
      toast('Promo code applied successfully!', {
        description: `You'll get ${promoData.discount_type === 'percentage' ? promoData.discount_value + '%' : '$' + promoData.discount_value} off`,
      });
    } catch (error) {
      toast('Error applying promo code', {
        description: 'Please try again later.',
      });
    }
  };

  // Function to get theme-aware colors
  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        plus: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        gradient: 'from-pegasus-blue-600 to-pegasus-blue-400',
        loader: 'text-pegasus-blue-600'
      };
    }
    return {
      plus: 'text-pegasus-orange',
      gradient: 'from-orange-600 to-orange-400',
      loader: 'text-pegasus-orange'
    };
  };

  // Function to determine the plan variant based on theme
  const getPlanVariant = (planName: string): 'plus' | 'pro' | 'max' | 'basic' => {
    const lowerPlanName = planName.toLowerCase();
    if (lowerPlanName.includes('max')) return 'max';
    if (lowerPlanName.includes('standard')) return 'pro';
    if (lowerPlanName.includes('basic')) return theme === 'hardware' ? 'basic' : 'basic';
    return theme === 'hardware' ? 'basic' : 'plus';
  };

  // Enhanced function to determine if plan is recommended and get popularity info
  const getPlanPopularity = (planName: string, index: number): {
    isRecommended: boolean;
    isMostPopular: boolean;
    badge: string | null;
  } => {
    const lowerPlanName = planName.toLowerCase();

    // Define specific plan priorities
    let isMostPopular = false;
    let isRecommended = false;
    let badge = null;

    // Most Popular: Pro plan (best balance of features and price)
    if (lowerPlanName.includes('pro') && !lowerPlanName.includes('max')) {
      isMostPopular = true;
      badge = 'Most Popular';
    }
    // Best Value: Max plan (highest value for money)
    else if (lowerPlanName === 'max' || (lowerPlanName.includes('max') && !lowerPlanName.includes('pro'))) {
      isRecommended = true;
      badge = 'Best Value';
    }
    // Starter: Basic plan
    else if (lowerPlanName.includes('basic')) {
      badge = 'Starter';
    }
    // Plus plan gets a special badge
    else if (lowerPlanName.includes('plus') && !lowerPlanName.includes('max')) {
      badge = 'Popular';
    }

    return { isRecommended, isMostPopular, badge };
  };

  // Pagination helper functions
  const totalPages = Math.ceil(plans.length / CARDS_PER_PAGE);
  const startIndex = currentPage * CARDS_PER_PAGE;
  const endIndex = startIndex + CARDS_PER_PAGE;
  const currentPlans = plans.slice(startIndex, endIndex);

  const nextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 0 && page < totalPages) {
      setCurrentPage(page);
    }
  };

  const themeColors = getThemeColors();

  return (
    <div id="pricing">
      {/* Main Pricing Section with Dark Background */}
      <div className="py-20 bg-[#111111] text-gray-300 relative">
        {/* Background gradient only for the header section */}
        <div className="absolute inset-0 z-0 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, rgba(17,17,17,0.8) 60%, transparent 100%)',
          height: '60%'
        }}></div>

        {/* Advanced Offer Banner */}
        {advancedOffer && (
          <div className="container mx-auto px-4 relative z-10">
            <AdvancedOfferBanner
              offer={advancedOffer}
              onApplyCode={handleApplyPromoCode}
              theme={theme}
            />
          </div>
        )}



        {/* Header Section */}
        <div className="container mx-auto px-4 relative z-10 mb-16">
          <div className="text-center">
            <div className="mb-6">
              <span className={`bg-[#1a1a1a] border border-gray-700 px-4 py-1 rounded-full text-xs sm:text-sm font-medium ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}`}>
               Pricing Plans
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
              Choose Your <span className={theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}>Perfect Plan</span>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
              Professional pricing plans designed for technicians and repair businesses
            </p>
          </div>
        </div>

        {/* Pricing Plans */}
          <div className="container mx-auto px-4 relative z-10">
          {loading ? (
            <div className="flex flex-col items-center justify-center min-h-[400px]">
              <Loader2 className={`h-12 w-12 ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'} animate-spin mb-4`} />
              <p className="text-lg text-gray-400">Loading pricing plans...</p>
            </div>
          ) : plans.length > 0 ? (
            <div className="max-w-7xl mx-auto pt-16 pb-12 px-4">
              {/* Pricing Cards with Pagination */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPage}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10 mb-12 min-h-[600px] items-start"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.4 }}
                >
                  {currentPlans.map((plan, index) => {
                  const features = generateFeatures(plan);
                  const perks = parsePerks(plan.perks);
                  const planVariant = getPlanVariant(plan.name_plan);
                  const popularity = getPlanPopularity(plan.name_plan, index);

                  // Apply discount if offer is active
                  const priceInfo = calculateDiscountedPrice(plan.price, plan.name_plan);

                  return (
                    <PricingCard
                      key={plan.id}
                      id={plan.id}
                      name={plan.name_plan}
                      price={priceInfo.discounted || priceInfo.original}
                      originalPrice={priceInfo.discounted ? priceInfo.original : undefined}
                      features={features}
                      perks={perks}
                      index={index}
                      recommended={popularity.isRecommended}
                      mostPopular={popularity.isMostPopular}
                      badge={popularity.badge}
                      variant={planVariant}
                      discountPercentage={priceInfo.discountPercentage}
                      validityPeriod={plan.validity_period}
                      onChoosePlan={() => handleChoosePlan(plan)}
                    />
                  );
                })}
                </motion.div>
              </AnimatePresence>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <motion.div
                  className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mt-12"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <Button
                    onClick={prevPage}
                    disabled={currentPage === 0}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",
                      theme === 'hardware' ? "hover:border-pegasus-blue-400" : "hover:border-orange-400"
                    )}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>

                  {/* Page indicators */}
                  <div className="flex space-x-2">
                    {Array.from({ length: totalPages }, (_, i) => (
                      <motion.button
                        key={i}
                        onClick={() => goToPage(i)}
                        className={cn(
                          "w-10 h-10 rounded-full text-sm font-medium transition-all duration-200 border",
                          currentPage === i
                            ? theme === 'hardware'
                              ? "bg-pegasus-blue-500 text-white border-pegasus-blue-400 shadow-lg"
                              : "bg-orange-500 text-white border-orange-400 shadow-lg"
                            : "bg-gray-700 text-gray-300 hover:bg-gray-600 border-gray-600 hover:border-gray-500"
                        )}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {i + 1}
                      </motion.button>
                    ))}
                  </div>

                  <Button
                    onClick={nextPage}
                    disabled={currentPage === totalPages - 1}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",
                      theme === 'hardware' ? "hover:border-pegasus-blue-400" : "hover:border-orange-400"
                    )}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>

                  {/* Page info */}
                  <div className="text-sm text-gray-400 sm:ml-4">
                    Page {currentPage + 1} of {totalPages}
                  </div>
                </motion.div>
              )}
            </div>
            ) : (
              <div className="text-center py-20 px-4 max-w-2xl mx-auto">
                <h3 className="text-2xl font-bold text-white mb-4">No pricing plans available at the moment</h3>
                <p className="text-gray-400 mb-6">
                  Please check back later or contact us for custom pricing tailored to your needs.
                </p>
                <Button
                  onClick={() => {
                    const contactSection = document.getElementById('contact');
                    if (contactSection) {
                      contactSection.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className={theme === 'hardware'
                    ? "bg-pegasus-blue hover:bg-pegasus-blue-700 text-white"
                    : "bg-pegasus-orange hover:bg-pegasus-orange-600 text-white"
                  }
                >
                  Contact Us
                </Button>
              </div>
            )}
        </div>
      </div>

      {/* Additional Sections - Outside the dark background */}
      {plans.length > 0 && (
        <div className="relative z-10">
          {showAllComponents ? (
            <>
              <PricingStats theme={theme} />
              <PlanComparison theme={theme} />
              <PricingTestimonials theme={theme} />
              <PricingFAQ theme={theme} />
            </>
          ) : (
            <div className="py-16 bg-[#111111] text-center">
              <div className="container mx-auto px-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    Want to see more?
                  </h3>
                  <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
                    Explore detailed plan comparisons, customer testimonials, statistics, and frequently asked questions on our dedicated pricing page.
                  </p>
                  <Button
                    onClick={() => navigate('/pricing')}
                    size="lg"
                    className={`${
                      theme === 'hardware'
                        ? 'bg-gradient-to-r from-pegasus-blue-500 to-pegasus-blue-600 hover:from-pegasus-blue-600 hover:to-pegasus-blue-700'
                        : 'bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700'
                    } text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105`}
                  >
                    Show More Details
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </motion.div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Pricing;
