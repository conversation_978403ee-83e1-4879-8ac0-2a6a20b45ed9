
import React, { useState, useEffect } from 'react';
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { CheckCircle2, Smartphone, Zap, RefreshCw } from "lucide-react";
import SupportedModels from '@/sections/SupportedModels';
import Pricing from '@/sections/Pricing';
import { use3DEffect } from '@/hooks/use3DEffect';

// ShinyText component
const ShinyText: React.FC<{ text: string; className?: string }> = ({ text, className = "" }) => (
  <span className={`relative overflow-hidden inline-block ${className}`}>
    {text}
    <span style={{
      position: 'absolute',
      inset: 0,
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
      animation: 'shine 2s infinite linear',
      opacity: 0.5,
      pointerEvents: 'none'
    }}></span>
    <style>{`
      @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
    `}</style>
  </span>
);

const Software = () => {
  const { toast: toastNotify } = useToast();
  const [latestUpdate, setLatestUpdate] = useState<{
    varizon: string;
    name: string | null;
    link: string | null;
  } | null>(null);

  // Add 3D effect for the software image card
  const softwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });
  const [stats, setStats] = useState({
    downloadCount: 0
  });
  
  const [homeImageUrl, setHomeImageUrl] = useState<string>();

  useEffect(() => {
    // Fetch home image from Supabase storage
    const fetchHomeImage = async () => {
      try {
        // Get home image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');
        
        if (imageData?.publicUrl) {
          setHomeImageUrl(imageData.publicUrl);
        }
      } catch (error) {
        toastNotify({
          title: "Image Loading Error",
          description: "Could not load the home image. Using fallback image instead.",
          variant: "destructive",
        });
      }
    };

    // Fetch latest software update
    const fetchLatestUpdate = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestUpdate(data[0]);
        }
      } catch (error) {
        // Error handling without console output
      }
    };

    // Fetch statistics
    const fetchStats = async () => {
      try {

        const { data: updateData, error: updateError } = await supabase
          .from('update')
          .select('download_count')
          .order('release_at', { ascending: false })
          .limit(1);



        setStats({
          downloadCount: updateData?.[0]?.download_count || 0
        });
      } catch (error) {
        // Error handling without console output
      }
    };

    fetchHomeImage();
    fetchLatestUpdate();
    fetchStats();
  }, []);

  const handleDownload = async () => {
    try {
      if (latestUpdate?.link) {
        // Call the increment_counter function
        const { data, error: counterError } = await supabase.rpc('increment_counter');

        if (counterError) {
          toast.error('Failed to process download request');
        } else {

          // Open the download link
          window.location.href = latestUpdate.link;
        }
      } else {
        toast.info("Download link is not available at the moment. Please try again later.");
      }
    } catch (error) {
      // Still provide download link even if counting fails
      if (latestUpdate?.link) {
        window.location.href = latestUpdate.link;
      }
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <div className="pt-[30px] relative bg-[#111111] text-gray-300 min-h-screen flex flex-col overflow-x-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center justify-between min-h-[80vh]">

            {/* Content Section */}
            <motion.div
              className="lg:w-1/2 text-center lg:text-left mb-10 lg:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="mb-6"
              >
                <ShinyText
                  text="Professional Software Solutions"
                  className="bg-[#1a1a1a] border border-gray-700 text-orange-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer hover:border-orange-400/50 transition-colors"
                />
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-4xl sm:text-5xl lg:text-6xl font-semibold text-white leading-tight mb-6"
              >
                Pegasus Tool for<br />
                <span className="text-orange-400">Software</span> Solutions
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-lg lg:text-xl text-gray-400 max-w-lg mb-8"
              >
                Professional software solution for device unlocking, firmware flashing, and repair operations with support for 200+ smartphone models and enterprise-grade reliability for technicians worldwide.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-col sm:flex-row items-center lg:items-start lg:justify-start justify-center gap-4 mb-8"
              >
                <motion.button
                  onClick={handleDownload}
                  className="bg-orange-500 text-white px-6 py-3 rounded-md text-sm font-semibold hover:bg-orange-500/80 transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md"
                  whileHover={{ scale: 1.03, y: -1 }}
                  whileTap={{ scale: 0.97 }}
                  transition={{ type: "spring", stiffness: 400, damping: 15 }}
                >
                  Get Pegasus Tool {latestUpdate && `v${latestUpdate.varizon}`}
                </motion.button>

                <motion.button
                  onClick={() => document.getElementById('software-features')?.scrollIntoView({ behavior: 'smooth' })}
                  className="bg-transparent border-2 border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white px-6 py-3 rounded-md text-sm font-semibold transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md"
                  whileHover={{ scale: 1.03, y: -1 }}
                  whileTap={{ scale: 0.97 }}
                  transition={{ type: "spring", stiffness: 400, damping: 15 }}
                >
                  Explore Features
                </motion.button>
              </motion.div>
            </motion.div>

            {/* Image Section */}
            <motion.div
              className="lg:w-1/2 relative"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <div className="relative flex items-center justify-center" style={{ minHeight: '400px' }}>
                {/* Glowing background effect */}
                <motion.div
                  className="bg-gradient-to-br from-orange-500/20 via-orange-400/15 to-orange-700/10 rounded-full h-80 w-80 lg:h-[420px] lg:w-[420px] mx-auto absolute blur-3xl"
                  animate={{
                    scale: [1, 1.05, 1],
                    opacity: [0.5, 0.8, 0.5],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />

                <motion.div
                  ref={softwareImageRef}
                  className="relative z-20 max-w-full lg:max-w-md mx-auto bg-[#1a1a1a]/40 backdrop-blur-sm rounded-2xl p-6 border border-orange-500/20 shadow-2xl"
                  animate={{
                    y: [0, -8, 0],
                  }}
                  transition={{
                    duration: 7,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  <img
                    src={homeImageUrl}
                    alt="Pegasus Tool - Advanced Mobile Device Management Interface"
                    className="w-full h-full object-contain rounded-lg"
                    style={{
                      filter: 'drop-shadow(0 15px 25px rgba(0, 0, 0, 0.6)) brightness(1.1) contrast(1.05)'
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "";
                    }}
                  />

                  {/* Animated interface points */}
                  <motion.div
                    className="absolute top-1/4 left-1/4 w-3 h-3 bg-orange-400 rounded-full shadow-lg"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />
                  <motion.div
                    className="absolute top-1/2 right-1/3 w-2 h-2 bg-orange-300 rounded-full shadow-lg"
                    animate={{
                      scale: [1, 1.4, 1],
                      opacity: [0.6, 1, 0.6],
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 0.5
                    }}
                  />
                  <motion.div
                    className="absolute bottom-1/3 left-1/3 w-3 h-3 bg-orange-200 rounded-full shadow-lg"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.8, 1, 0.8],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 1
                    }}
                  />
                </motion.div>
              </div>
            </motion.div>

          </div>
        </div>
      </div>

      {/* Features Section */}
      <section id="software-features" className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-orange-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
                Software Features
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
              Discover the power of <span className="text-orange-400">Pegasus Tool</span>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
              Professional software solution with advanced features for device unlocking and repair
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto"
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, margin: "-100px" }}
          >
            {[
              {
                title: 'Professional Interface',
                icon: CheckCircle2,
                description: 'Enterprise-grade interface designed for technicians and repair professionals with streamlined workflows and advanced diagnostics.'
              },
              {
                title: 'Universal Compatibility',
                icon: Smartphone,
                description: 'Comprehensive support for 200+ device models including Xiaomi, Samsung, Oppo, Vivo, Realme, and emerging brands.'
              },
              {
                title: 'Continuous Innovation',
                icon: RefreshCw,
                description: 'Regular updates with new device support, security patches, and cutting-edge features to stay ahead of industry demands.'
              },
              {
                title: 'Enterprise Support',
                icon: Zap,
                description: 'Professional technical support with comprehensive documentation and training resources for repair technicians.'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-orange-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-orange-900/30 to-orange-800/20 flex items-center justify-center shadow-md">
                      <feature.icon className="h-6 w-6 text-orange-400" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-white">{feature.title}</h3>
                      <p className="text-gray-400">{feature.description}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Supported Models Section */}
      <SupportedModels theme="software" />

      {/* Pricing Section */}
      <Pricing theme="software" showAllComponents={false} />
    </div>
  );
};

export default Software;



